package service

import (
	"encoding/json"
	"fmt"
	"gin-server/config"
	"gin-server/configmanager/config_exec/controller"
	"gin-server/configmanager/config_exec/model"
	"gin-server/database"
	"gin-server/database/models"
	"log"
	"os"
	"path/filepath"
	"sync"
	"time"
)

// BatchOperation 批量操作项
type BatchOperation struct {
	Category  string `json:"category"`  // "user_info", "device_info"
	TargetID  string `json:"target_id"` // 用户ID或设备ID
	Operation string `json:"operation"` // "insert", "update", "delete"
}

// ConfigGenTrigger 配置生成触发器接口
type ConfigGenTrigger interface {
	TriggerUserOperation(userID, gatewayID, operation string) error
	TriggerDeviceOperation(deviceID, operation string) error
	TriggerUserConnectionOperation(userID, gatewayID, operation string) error
	TriggerBatchOperations(gatewayID string, operations []BatchOperation) error
}

// Manager 配置文件管理器
type Manager struct {
	cfg              *config.Config
	fetcher          *controller.Fetcher
	processor        *controller.Processor
	executor         *controller.Executor
	approvalManager  *controller.ApprovalManager
	fieldChecker     *controller.FieldChecker // 新增：字段检查器
	configGenTrigger ConfigGenTrigger         // 新增：配置生成触发器
	running          bool
	stopChan         chan struct{}
	wg               sync.WaitGroup
}

// NewManager 创建新的配置文件管理器
func NewManager(cfg *config.Config) (*Manager, error) {
	// 初始化处理器
	processor := controller.NewProcessor(cfg)

	// 初始化获取器
	fetcher, err := controller.NewFetcher(cfg)
	if err != nil {
		return nil, fmt.Errorf("创建文件获取器失败: %w", err)
	}

	// 初始化执行器
	executor := controller.NewExecutor(cfg, processor)

	// 初始化批准管理器
	approvalManager := controller.NewApprovalManager(cfg, processor)

	// 初始化字段检查器
	fieldChecker := controller.NewFieldChecker(cfg)

	// 初始化配置生成触发器
	configGenTrigger, err := NewConfigGenAdapter()
	if err != nil {
		log.Printf("[WARNING] 配置生成触发器初始化失败: %v，将跳过配置生成联动功能", err)
		configGenTrigger = nil
	} else {
		log.Printf("[INFO] 配置生成触发器初始化成功")
	}

	manager := &Manager{
		cfg:              cfg,
		fetcher:          fetcher,
		processor:        processor,
		executor:         executor,
		approvalManager:  approvalManager,
		fieldChecker:     fieldChecker,
		configGenTrigger: configGenTrigger,
		stopChan:         make(chan struct{}),
	}

	// 设置批准管理器的执行器（避免循环依赖）
	approvalManager.SetExecutor(manager)

	// 设置批准管理器的处理信息提供者
	approvalManager.SetProcessingInfoProvider(manager)

	return manager, nil
}

// Start 启动配置文件管理器服务
func (m *Manager) Start() error {
	if m.running {
		return fmt.Errorf("服务已经在运行")
	}

	m.running = true
	log.Println("[INFO] ====== 配置文件管理器服务启动 ======")
	log.Printf("[INFO] 配置信息: 轮询间隔=%d秒", m.cfg.ConfigManager.ConfigFileManager.PollInterval)
	log.Printf("[INFO] 远程路径: %s", m.cfg.ConfigManager.ConfigFileManager.RemotePath)
	log.Printf("[INFO] 下载目录: %s", m.cfg.ConfigManager.ConfigFileManager.DownloadDir)
	log.Printf("[INFO] 时间戳文件: %s", m.cfg.ConfigManager.ConfigFileManager.TimestampFile)
	log.Printf("[INFO] 增量下载机制: 已启用")
	log.Printf("[INFO] 远程文件删除: 已禁用（保留远程文件）")

	// 启动周期性检查任务
	m.wg.Add(1)
	go m.runPeriodic()

	return nil
}

// Stop 停止配置文件管理器服务
func (m *Manager) Stop() error {
	if !m.running {
		return nil
	}

	log.Println("[INFO] ====== 正在停止配置文件管理器服务... ======")
	close(m.stopChan)
	m.wg.Wait()
	m.running = false

	// 关闭获取器
	if err := m.fetcher.Close(); err != nil {
		log.Printf("[ERROR] 关闭获取器时出错: %v", err)
	} else {
		log.Println("[INFO] 获取器已关闭")
	}

	log.Println("[INFO] ====== 配置文件管理器服务已停止 ======")
	return nil
}

// runPeriodic 运行周期性任务
func (m *Manager) runPeriodic() {
	defer m.wg.Done()

	log.Printf("[INFO] 开始周期性检查，间隔: %d 秒", m.cfg.ConfigManager.ConfigFileManager.PollInterval)

	ticker := time.NewTicker(time.Duration(m.cfg.ConfigManager.ConfigFileManager.PollInterval) * time.Second)
	defer ticker.Stop()

	// 立即执行一次检查
	log.Println("[INFO] 执行初始配置文件检查...")
	m.checkAndProcessFiles()

	for {
		select {
		case <-ticker.C:
			// 定期执行检查
			log.Println("[INFO] 执行周期性配置文件检查...")
			m.checkAndProcessFiles()
		case <-m.stopChan:
			log.Println("[INFO] 周期性检查任务结束")
			return
		}
	}
}

// checkAndProcessFiles 检查并处理配置文件
func (m *Manager) checkAndProcessFiles() {
	log.Println("[INFO] ====== 开始检查配置文件 ======")
	startTime := time.Now()

	// 获取配置文件
	log.Println("[INFO] 正在从远程获取配置文件...")
	configFiles, err := m.fetcher.FetchConfigFiles()
	if err != nil {
		log.Printf("[ERROR] 获取配置文件失败: %v", err)
		return
	}

	log.Printf("[INFO] 获取到 %d 个配置文件", len(configFiles))

	if len(configFiles) > 0 {
		// 获取数据库连接
		log.Println("[INFO] 正在获取数据库连接...")
		db, err := database.GetDB()
		if err != nil {
			log.Printf("[ERROR] 获取数据库连接失败: %v", err)
			return
		}
		log.Println("[INFO] 数据库连接获取成功")

		// 用于存储需要处理的文件
		var filesToProcess []*model.ConfigFile

		// 先检查文件是否已存在于数据库
		log.Println("[INFO] 开始检查文件是否存在于数据库中...")
		for _, file := range configFiles {
			var count int64
			err := db.Model(&model.ConfigFile{}).Where("filename = ?", file.Filename).Count(&count).Error
			if err != nil {
				log.Printf("[ERROR] 检查文件 %s 是否存在时出错: %v", file.Filename, err)
				continue
			}

			if count > 0 {
				log.Printf("[INFO] 文件 %s 已存在于数据库中，跳过处理", file.Filename)
				// 注意：增量下载机制下，远程文件将被保留，不再删除
			} else {
				log.Printf("[INFO] 文件 %s 不存在于数据库中，准备处理", file.Filename)
				filesToProcess = append(filesToProcess, file)
			}
		}

		// 注意：远程文件删除功能已在增量下载机制重构中移除
		// 远程文件将被保留，以支持增量下载和避免数据丢失
		log.Printf("[INFO] 使用增量下载机制，远程文件将被保留，本次处理 %d 个新文件", len(filesToProcess))

		// 处理需要保存的文件
		for _, file := range filesToProcess {
			log.Printf("[INFO] 开始保存文件到数据库: %s", file.Filename)
			if err := m.processor.SaveConfigFile(file); err != nil {
				log.Printf("[ERROR] 保存配置文件 %s 失败: %v", file.Filename, err)
				continue
			}
			log.Printf("[INFO] 文件 %s 已成功保存到数据库", file.Filename)

			// 处理文件
			log.Printf("[INFO] 开始异步处理文件: %s", file.Filename)
			go m.processConfigFile(file)
		}
	} else {
		log.Println("[INFO] 没有发现新的配置文件")
	}

	elapsed := time.Since(startTime)
	log.Printf("[INFO] ====== 配置文件检查完成，耗时: %v ======", elapsed)
}

// processConfigFile 处理单个配置文件
func (m *Manager) processConfigFile(file *model.ConfigFile) {
	log.Printf("[INFO] 开始处理配置文件: ID=%d, 文件名=%s, 类型=%s", file.ID, file.Filename, file.FileType)

	start := time.Now()

	// 设置处理状态
	file.Status = "processing"
	file.ProcessTime = &start
	if err := m.processor.SaveConfigFile(file); err != nil {
		log.Printf("[ERROR] 更新配置文件处理状态失败: %v", err)
	} else {
		log.Printf("[INFO] 文件状态已更新为 'processing'")
	}

	// 验证配置文件内容是否符合预期
	log.Printf("[INFO] 开始验证配置文件内容: %s", file.Filename)
	if err := m.processor.ValidateConfigFile(file); err != nil {
		log.Printf("[ERROR] 验证配置文件内容失败: %v", err)
		file.Status = "failed"
		file.Result = fmt.Sprintf("验证失败: %v", err)

		// 更新处理时间和状态
		processTime := time.Now()
		file.ProcessTime = &processTime
		if updateErr := m.processor.SaveConfigFile(file); updateErr != nil {
			log.Printf("[ERROR] 更新配置文件状态失败: %v", updateErr)
		} else {
			log.Printf("[INFO] 文件状态已更新为 'failed'（验证失败）")
		}

		return
	}
	log.Printf("[INFO] 配置文件验证通过: %s", file.Filename)

	// 执行字段检查
	log.Printf("[INFO] 开始检查配置文件字段完整性: %s", file.Filename)
	if err := m.fieldChecker.CheckGatewayFields(file.Content, file.FileType); err != nil {
		// 根据字段检查模式决定是否继续处理
		if m.cfg.ConfigManager.ConfigFileManager.FieldCheck.Mode == "strict" {
			log.Printf("[ERROR] 字段检查失败（严格模式）: %v", err)
			file.Status = "failed"
			file.Result = fmt.Sprintf("字段检查失败: %v", err)

			// 更新处理时间和状态
			processTime := time.Now()
			file.ProcessTime = &processTime
			if updateErr := m.processor.SaveConfigFile(file); updateErr != nil {
				log.Printf("[ERROR] 更新配置文件状态失败: %v", updateErr)
			} else {
				log.Printf("[INFO] 文件状态已更新为 'failed'（字段检查失败）")
			}
			return
		} else {
			log.Printf("[WARN] 字段检查警告（警告模式）: %v", err)
		}
	} else {
		log.Printf("[INFO] 配置文件字段检查通过: %s", file.Filename)
	}

	// 检查是否启用管理员批准功能
	if m.cfg.ConfigManager.ConfigFileManager.EnableAdminPermission {
		log.Printf("[INFO] 管理员批准功能已启用，提交配置文件到待批准列表: %s", file.Filename)
		// 提交到待批准列表
		if err := m.approvalManager.SubmitForApproval(file); err != nil {
			log.Printf("[ERROR] 提交配置文件到待批准列表失败: %v", err)
			file.Status = "failed"
			file.Result = fmt.Sprintf("提交批准失败: %v", err)

			// 更新处理时间和状态
			processTime := time.Now()
			file.ProcessTime = &processTime
			if updateErr := m.processor.SaveConfigFile(file); updateErr != nil {
				log.Printf("[ERROR] 更新配置文件状态失败: %v", updateErr)
			}
		} else {
			log.Printf("[INFO] 配置文件 %s 已提交到待批准列表，等待管理员批准", file.Filename)
		}
		return // 暂停执行，等待批准
	}

	// 执行配置文件
	log.Printf("[INFO] 开始执行配置文件: ID=%d, 文件名=%s", file.ID, file.Filename)
	result, err := m.executor.ParseAndExecute(file.ID)
	if err != nil {
		log.Printf("[ERROR] 执行配置文件失败: %v", err)
		file.Status = "failed"
		file.Result = fmt.Sprintf("执行失败: %v", err)
	} else {
		log.Printf("[INFO] 配置文件执行完成，执行状态: %s", result.Status)
		file.Status = result.Status

		// 将执行结果转为JSON字符串保存到文件记录中
		jsonBytes, err := result.Value()
		if err != nil {
			log.Printf("[ERROR] 序列化执行结果失败: %v", err)
			file.Result = fmt.Sprintf("序列化结果失败: %v", err)
		} else {
			file.Result = string(jsonBytes.([]byte))
			log.Printf("[INFO] 执行结果已序列化，长度: %d 字节", len(file.Result))
		}

		// 保存执行结果到结果表
		if err := m.processor.SaveExecutionResult(file.ID, result); err != nil {
			log.Printf("[ERROR] 保存执行结果失败: %v", err)
		} else {
			log.Printf("[INFO] 执行结果已保存到数据库，执行ID: %s", result.ExecutionID)
		}

		// 触发配置生成（仅在执行成功时）
		if result.Status == "success" || result.Status == "partial" {
			m.triggerConfigGeneration(file, result)
		}
	}

	// 更新处理时间和状态
	processTime := time.Now()
	file.ProcessTime = &processTime
	if err := m.processor.SaveConfigFile(file); err != nil {
		log.Printf("[ERROR] 更新配置文件状态失败: %v", err)
	} else {
		log.Printf("[INFO] 文件最终状态已更新为 '%s'", file.Status)
	}

	elapsed := time.Since(start)
	log.Printf("[INFO] 配置文件 %s 处理完成，状态: %s，耗时: %v", file.Filename, file.Status, elapsed)
}

// ProcessFileByID 通过ID处理配置文件(手动触发)
func (m *Manager) ProcessFileByID(id uint) error {
	log.Printf("[INFO] 收到手动处理请求，配置文件ID: %d", id)

	// 加载配置文件
	file, err := m.processor.LoadConfigFile(id)
	if err != nil {
		log.Printf("[ERROR] 加载配置文件失败，ID=%d: %v", id, err)
		return fmt.Errorf("加载配置文件失败: %w", err)
	}
	log.Printf("[INFO] 成功加载配置文件: ID=%d, 文件名=%s", id, file.Filename)

	// 后台处理
	log.Printf("[INFO] 开始异步处理配置文件: ID=%d", id)
	go m.processConfigFile(file)
	return nil
}

// GetFiles 获取配置文件列表
func (m *Manager) GetFiles(limit, offset int, statusFilter string) ([]model.ConfigFile, error) {
	db, err := database.GetDB()
	if err != nil {
		return nil, err
	}

	var files []model.ConfigFile
	query := db

	// 添加状态过滤
	if statusFilter != "" {
		query = query.Where("status = ?", statusFilter)
	}

	// 分页和排序
	result := query.Order("id desc").Limit(limit).Offset(offset).Find(&files)
	if result.Error != nil {
		return nil, result.Error
	}

	return files, nil
}

// GetFileByID 通过ID获取配置文件
func (m *Manager) GetFileByID(id uint) (*model.ConfigFile, error) {
	return m.processor.LoadConfigFile(id)
}

// GetResult 获取执行结果
func (m *Manager) GetResult(fileID uint) (*model.ExecutionResult, error) {
	db, err := database.GetDB()
	if err != nil {
		return nil, err
	}

	var record model.ResultRecord
	if err := db.Where("config_file_id = ?", fileID).Order("id desc").First(&record).Error; err != nil {
		return nil, err
	}

	var result model.ExecutionResult
	if err := result.Scan([]byte(record.DetailJSON)); err != nil {
		return nil, fmt.Errorf("解析执行结果失败: %w", err)
	}

	return &result, nil
}

// ExecuteApprovedConfigFile 执行已批准的配置文件（实现ConfigExecutor接口）
func (m *Manager) ExecuteApprovedConfigFile(configFileID uint) error {
	log.Printf("[INFO] [Manager] 开始执行已批准的配置文件 - 配置文件ID: %d", configFileID)

	// 加载配置文件
	file, err := m.processor.LoadConfigFile(configFileID)
	if err != nil {
		log.Printf("[ERROR] [Manager] 加载配置文件失败 - 配置文件ID: %d, 错误: %v", configFileID, err)
		return fmt.Errorf("加载配置文件失败: %w", err)
	}

	log.Printf("[DEBUG] [Manager] 配置文件加载成功 - 配置文件ID: %d, 文件名: %s, 文件类型: %s, 当前状态: %s, 批准状态: %s",
		file.ID, file.Filename, file.FileType, file.Status, file.ApprovalStatus)

	// 验证批准状态
	if file.ApprovalStatus != "approved" {
		log.Printf("[ERROR] [Manager] 配置文件未获得批准 - 配置文件ID: %d, 文件名: %s, 当前批准状态: %s, 期望状态: approved",
			file.ID, file.Filename, file.ApprovalStatus)
		return fmt.Errorf("配置文件未获得批准，当前状态: %s", file.ApprovalStatus)
	}

	log.Printf("[INFO] [Manager] 配置文件批准状态验证通过，开始执行核心逻辑 - 配置文件ID: %d, 文件名: %s",
		file.ID, file.Filename)

	// 执行配置文件的核心逻辑（从processConfigFile方法中提取）
	return m.executeConfigFileCore(file)
}

// executeConfigFileCore 执行配置文件的核心逻辑
func (m *Manager) executeConfigFileCore(file *model.ConfigFile) error {
	start := time.Now()
	log.Printf("[INFO] [Manager] 开始执行配置文件核心逻辑 - 配置文件ID: %d, 文件名: %s, 文件类型: %s",
		file.ID, file.Filename, file.FileType)

	// 执行配置文件
	log.Printf("[DEBUG] [Manager] 调用执行器解析并执行配置文件 - 配置文件ID: %d", file.ID)
	result, err := m.executor.ParseAndExecute(file.ID)
	if err != nil {
		log.Printf("[ERROR] [Manager] 执行配置文件失败 - 配置文件ID: %d, 文件名: %s, 错误: %v",
			file.ID, file.Filename, err)
		file.Status = "failed"
		file.Result = fmt.Sprintf("执行失败: %v", err)
	} else {
		log.Printf("[INFO] [Manager] 配置文件执行完成 - 配置文件ID: %d, 文件名: %s, 执行状态: %s, 执行ID: %s",
			file.ID, file.Filename, result.Status, result.ExecutionID)
		file.Status = result.Status

		// 记录执行结果详情
		if len(result.Details.Operations) > 0 {
			log.Printf("[DEBUG] [Manager] 执行结果详情 - 配置文件ID: %d, 总操作数: %d, 成功数: %d, 失败数: %d",
				file.ID, result.Summary.TotalItems, result.Summary.SuccessItems, result.Summary.FailedItems)
		}

		// 将执行结果转为JSON字符串保存到文件记录中
		jsonBytes, err := result.Value()
		if err != nil {
			log.Printf("[ERROR] [Manager] 序列化执行结果失败 - 配置文件ID: %d, 文件名: %s, 错误: %v",
				file.ID, file.Filename, err)
			file.Result = fmt.Sprintf("序列化结果失败: %v", err)
		} else {
			file.Result = string(jsonBytes.([]byte))
			log.Printf("[DEBUG] [Manager] 执行结果已序列化 - 配置文件ID: %d, 结果长度: %d 字节",
				file.ID, len(file.Result))
		}

		// 保存执行结果到结果表
		if err := m.processor.SaveExecutionResult(file.ID, result); err != nil {
			log.Printf("[ERROR] [Manager] 保存执行结果失败 - 配置文件ID: %d, 文件名: %s, 执行ID: %s, 错误: %v",
				file.ID, file.Filename, result.ExecutionID, err)
		} else {
			log.Printf("[INFO] [Manager] 执行结果已保存到数据库 - 配置文件ID: %d, 执行ID: %s",
				file.ID, result.ExecutionID)
		}

		// 触发配置生成（仅在执行成功时）
		if result.Status == "success" || result.Status == "partial" {
			log.Printf("[INFO] [Manager] 执行成功，触发配置生成 - 配置文件ID: %d, 文件类型: %s, 状态: %s",
				file.ID, file.FileType, result.Status)
			m.triggerConfigGeneration(file, result)
		} else {
			log.Printf("[DEBUG] [Manager] 执行状态不满足配置生成条件 - 配置文件ID: %d, 状态: %s",
				file.ID, result.Status)
		}
	}

	// 更新处理时间和状态
	processTime := time.Now()
	file.ProcessTime = &processTime
	log.Printf("[DEBUG] [Manager] 更新配置文件最终状态 - 配置文件ID: %d, 文件名: %s, 最终状态: %s",
		file.ID, file.Filename, file.Status)

	if err := m.processor.SaveConfigFile(file); err != nil {
		log.Printf("[ERROR] [Manager] 更新配置文件状态失败 - 配置文件ID: %d, 文件名: %s, 状态: %s, 错误: %v",
			file.ID, file.Filename, file.Status, err)
		return fmt.Errorf("更新配置文件状态失败: %w", err)
	} else {
		log.Printf("[INFO] [Manager] 配置文件状态已成功更新 - 配置文件ID: %d, 文件名: %s, 最终状态: %s, 处理时间: %s",
			file.ID, file.Filename, file.Status, processTime.Format("2006-01-02 15:04:05"))
	}

	elapsed := time.Since(start)
	log.Printf("[INFO] [Manager] 配置文件执行完成 - 配置文件ID: %d, 文件名: %s, 文件类型: %s, 最终状态: %s, 总耗时: %v",
		file.ID, file.Filename, file.FileType, file.Status, elapsed)
	return nil
}

// GetPendingApprovalsByTimeRange 根据时间范围获取待批准的配置文件
func (m *Manager) GetPendingApprovalsByTimeRange(startTime, endTime time.Time) ([]model.ConfigFileResponse, error) {
	return m.approvalManager.GetPendingApprovalsByTimeRange(startTime, endTime)
}

// ProcessApprovalResults 处理批准结果
func (m *Manager) ProcessApprovalResults(approvals []model.ApprovalRequest) (*model.ApprovalBatchResult, error) {
	return m.approvalManager.ProcessApprovalResults(approvals)
}

// triggerConfigGeneration 触发配置生成
func (m *Manager) triggerConfigGeneration(file *model.ConfigFile, result *model.ExecutionResult) {
	if m.configGenTrigger == nil {
		log.Printf("[WARNING] 配置生成触发器未初始化，跳过配置生成联动")
		log.Printf("[DIAGNOSTIC] 请检查 ConfigGenAdapter 初始化是否成功")
		return
	}

	log.Printf("[INFO] 开始触发配置生成，文件类型: %s, 执行状态: %s", file.FileType, result.Status)
	log.Printf("[DIAGNOSTIC] 配置文件ID: %d, 文件名: %s", file.ID, file.Filename)

	// 异步触发配置生成，避免阻塞主流程
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("[ERROR] 配置生成触发过程中发生panic: %v", r)
				log.Printf("[DIAGNOSTIC] 文件类型: %s, 文件ID: %d", file.FileType, file.ID)
			}
		}()

		// 检查执行结果中的操作数量
		operationCount := len(result.Details.Operations)
		log.Printf("[DIAGNOSTIC] 执行结果包含 %d 个操作", operationCount)

		if operationCount == 0 {
			log.Printf("[WARNING] 执行结果中没有操作详情，跳过配置生成")
			return
		}

		// 根据文件类型和执行结果触发相应的配置生成
		switch file.FileType {
		case "terminal":
			log.Printf("[DIAGNOSTIC] 触发终端配置生成: create")
			m.triggerTerminalConfigGeneration(result, "create")
		case "del_terminal":
			log.Printf("[DIAGNOSTIC] 触发终端配置生成: delete")
			m.triggerTerminalConfigGeneration(result, "delete")
		case "gateway":
			log.Printf("[DIAGNOSTIC] 触发网关配置生成: create")
			m.triggerGatewayConfigGeneration(result, "create")
		case "del_gateway":
			log.Printf("[DIAGNOSTIC] 触发网关配置生成: delete")
			m.triggerGatewayConfigGeneration(result, "delete")
		case "terminal_connect":
			log.Printf("[DIAGNOSTIC] 触发终端连接配置生成")
			m.triggerTerminalConnectionConfigGeneration(result)
		case "batch":
			log.Printf("[DIAGNOSTIC] 触发批量配置生成")
			m.triggerBatchConfigGeneration(result)
		default:
			log.Printf("[WARNING] 不支持的配置文件类型，跳过配置生成: %s", file.FileType)
			log.Printf("[DIAGNOSTIC] 支持的类型: terminal, del_terminal, gateway, del_gateway, terminal_connect, batch")
		}
	}()
}

// triggerTerminalConfigGeneration 触发终端配置生成
func (m *Manager) triggerTerminalConfigGeneration(result *model.ExecutionResult, operation string) {
	log.Printf("[INFO] 触发终端配置生成，操作类型: %s", operation)

	successCount := 0
	failureCount := 0
	skipCount := 0

	// 遍历执行结果中的操作详情
	for i, op := range result.Details.Operations {
		log.Printf("[DIAGNOSTIC] 处理操作 %d: 状态=%s, 类型=%s", i+1, op.Status, op.ItemResult.Type)

		if op.Status == "success" && op.ItemResult.Type == "terminal" {
			// 从操作详情中提取终端信息
			if terminal, ok := op.ItemResult.Data.(model.Terminal); ok {
				userID := terminal.BusinessID
				gatewayID := terminal.GatewayID

				log.Printf("[INFO] 触发用户配置生成: 用户ID=%s, 网关ID=%s, 操作=%s", userID, gatewayID, operation)
				log.Printf("[DIAGNOSTIC] 终端详情: BusinessID=%s, GatewayID=%s", terminal.BusinessID, terminal.GatewayID)

				if err := m.configGenTrigger.TriggerUserOperation(userID, gatewayID, operation); err != nil {
					log.Printf("[WARNING] 触发用户配置生成失败: %v", err)
					log.Printf("[DIAGNOSTIC] 失败详情: 用户ID=%s, 网关ID=%s, 错误=%v", userID, gatewayID, err)
					failureCount++
				} else {
					log.Printf("[SUCCESS] 用户配置生成触发成功: 用户ID=%s, 网关ID=%s", userID, gatewayID)
					successCount++
				}
			} else {
				log.Printf("[WARNING] 无法解析终端数据，跳过配置生成触发")
				log.Printf("[DIAGNOSTIC] 数据类型断言失败，实际类型: %T", op.ItemResult.Data)
				skipCount++
			}
		} else {
			log.Printf("[DIAGNOSTIC] 跳过操作: 状态=%s, 类型=%s", op.Status, op.ItemResult.Type)
			skipCount++
		}
	}

	log.Printf("[SUMMARY] 终端配置生成触发完成: 成功=%d, 失败=%d, 跳过=%d", successCount, failureCount, skipCount)
}

// triggerGatewayConfigGeneration 触发网关配置生成
func (m *Manager) triggerGatewayConfigGeneration(result *model.ExecutionResult, operation string) {
	log.Printf("[INFO] 触发网关配置生成，操作类型: %s", operation)

	// 遍历执行结果中的操作详情
	for _, op := range result.Details.Operations {
		if op.Status == "success" && op.ItemResult.Type == "gateway" {
			// 从操作详情中提取网关信息
			if gateway, ok := op.ItemResult.Data.(model.Gateway); ok {
				deviceID := gateway.BusinessID

				log.Printf("[INFO] 触发设备配置生成: 设备ID=%s, 操作=%s", deviceID, operation)

				if err := m.configGenTrigger.TriggerDeviceOperation(deviceID, operation); err != nil {
					log.Printf("[WARNING] 触发设备配置生成失败: %v", err)
				} else {
					log.Printf("[SUCCESS] 设备配置生成触发成功: 设备ID=%s", deviceID)
				}
			} else {
				log.Printf("[WARNING] 无法解析网关数据，跳过配置生成触发")
			}
		}
	}
}

// triggerTerminalConnectionConfigGeneration 触发终端连接配置生成
func (m *Manager) triggerTerminalConnectionConfigGeneration(result *model.ExecutionResult) {
	log.Printf("[INFO] 触发终端连接配置生成")

	// 使用map来去重，避免同一用户重复触发配置生成
	userGatewayMap := make(map[string]string) // userID -> gatewayID

	// 遍历执行结果中的操作详情，收集涉及的用户
	for _, op := range result.Details.Operations {
		if op.Status == "success" && op.ItemResult.Type == "terminal_connect" {
			// 从操作详情中提取连接信息
			if connect, ok := op.ItemResult.Data.(model.TerminalConnect); ok {
				userID := connect.PreID

				// 如果已经处理过这个用户，跳过
				if _, exists := userGatewayMap[userID]; exists {
					continue
				}

				// 需要查询用户所属的网关ID
				gatewayID := m.getUserGatewayID(userID)
				if gatewayID == "" {
					log.Printf("[WARNING] 无法获取用户 %s 的网关ID，跳过配置生成触发", userID)
					continue
				}

				// 记录用户和网关的映射关系
				userGatewayMap[userID] = gatewayID
			} else {
				log.Printf("[WARNING] 无法解析终端连接数据，跳过配置生成触发")
			}
		}
	}

	// 对去重后的用户触发配置生成
	for userID, gatewayID := range userGatewayMap {
		log.Printf("[INFO] 触发用户连接配置生成: 用户ID=%s, 网关ID=%s", userID, gatewayID)

		if err := m.configGenTrigger.TriggerUserConnectionOperation(userID, gatewayID, "modify"); err != nil {
			log.Printf("[WARNING] 触发用户连接配置生成失败: %v", err)
		} else {
			log.Printf("[SUCCESS] 用户连接配置生成触发成功: 用户ID=%s, 网关ID=%s", userID, gatewayID)
		}
	}

	log.Printf("[INFO] 终端连接配置生成触发完成，共处理 %d 个用户", len(userGatewayMap))
}

// triggerBatchConfigGeneration 触发批量配置生成
func (m *Manager) triggerBatchConfigGeneration(result *model.ExecutionResult) {
	log.Printf("[INFO] 触发批量配置生成")

	// 按网关ID分组操作
	gatewayOperations := make(map[string][]BatchOperation)

	// 遍历执行结果中的操作详情，收集操作信息
	for _, op := range result.Details.Operations {
		if op.Status != "success" {
			continue
		}

		switch op.ItemResult.Type {
		case "terminal":
			if terminal, ok := op.ItemResult.Data.(model.Terminal); ok {
				operation := "create"
				if op.OperationType == "delete_terminal" {
					operation = "delete"
				}

				userID := terminal.BusinessID
				gatewayID := terminal.GatewayID

				// 添加到对应网关的操作列表
				gatewayOperations[gatewayID] = append(gatewayOperations[gatewayID], BatchOperation{
					Category:  "user_info",
					TargetID:  userID,
					Operation: operation,
				})

				log.Printf("[INFO] 收集用户操作: 网关ID=%s, 用户ID=%s, 操作=%s", gatewayID, userID, operation)
			}
		case "gateway":
			if gateway, ok := op.ItemResult.Data.(model.Gateway); ok {
				operation := "create"
				if op.OperationType == "delete_gateway" {
					operation = "delete"
				}

				deviceID := gateway.BusinessID

				// 网关操作使用设备ID作为网关ID
				gatewayOperations[deviceID] = append(gatewayOperations[deviceID], BatchOperation{
					Category:  "device_info",
					TargetID:  deviceID,
					Operation: operation,
				})

				log.Printf("[INFO] 收集设备操作: 设备ID=%s, 操作=%s", deviceID, operation)
			}
		case "terminal_connect":
			if connect, ok := op.ItemResult.Data.(model.TerminalConnect); ok {
				userID := connect.PreID
				gatewayID := m.getUserGatewayID(userID)
				if gatewayID != "" {
					// 添加到对应网关的操作列表
					gatewayOperations[gatewayID] = append(gatewayOperations[gatewayID], BatchOperation{
						Category:  "user_info",
						TargetID:  userID,
						Operation: "modify",
					})

					log.Printf("[INFO] 收集用户连接操作: 网关ID=%s, 用户ID=%s", gatewayID, userID)
				}
			}
		}
	}

	// 按网关分别触发批量配置生成
	for gatewayID, operations := range gatewayOperations {
		if len(operations) == 0 {
			continue
		}

		log.Printf("[INFO] 为网关 %s 触发批量配置生成，操作数量: %d", gatewayID, len(operations))

		// 使用新的批量触发方法
		if err := m.configGenTrigger.TriggerBatchOperations(gatewayID, operations); err != nil {
			log.Printf("[WARNING] 网关 %s 的批量配置生成失败: %v", gatewayID, err)
		} else {
			log.Printf("[SUCCESS] 网关 %s 的批量配置生成触发成功，操作数量: %d", gatewayID, len(operations))
		}
	}
}

// getUserGatewayID 获取用户所属的网关ID
func (m *Manager) getUserGatewayID(userID string) string {
	db, err := database.GetDB()
	if err != nil {
		log.Printf("[ERROR] 获取数据库连接失败: %v", err)
		return ""
	}

	var user models.User
	if err := db.Where("user_id = ?", userID).First(&user).Error; err != nil {
		log.Printf("[ERROR] 查询用户 %s 失败: %v", userID, err)
		return ""
	}

	return user.GatewayDeviceID
}

// GetConfigFilesUnified 统一的配置文件查询方法，支持基础查询和详细处理结果查询
func (m *Manager) GetConfigFilesUnified(params model.ConfigQueryParams) (*model.ConfigQueryResponse, error) {
	// 设置默认值
	if params.Page <= 0 {
		params.Page = 1
	}
	if params.PageSize <= 0 {
		params.PageSize = 20
	}
	if params.PageSize > 100 {
		params.PageSize = 100
	}
	if params.ContentSource == "" {
		params.ContentSource = "auto"
	}

	// 解析时间参数
	var startTime, endTime time.Time
	var err error

	if params.StartTime != "" {
		startTime, err = time.ParseInLocation("2006-01-02 15:04:05", params.StartTime, time.UTC)
		if err != nil {
			return nil, fmt.Errorf("起始时间格式错误: %w", err)
		}
	}

	if params.EndTime != "" {
		endTime, err = time.ParseInLocation("2006-01-02 15:04:05", params.EndTime, time.UTC)
		if err != nil {
			return nil, fmt.Errorf("结束时间格式错误: %w", err)
		}
	}

	// 验证时间范围
	if !startTime.IsZero() && !endTime.IsZero() && endTime.Before(startTime) {
		return nil, fmt.Errorf("结束时间不能早于起始时间")
	}

	// 构建查询条件
	db, err := database.GetDB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	query := db.Model(&model.ConfigFile{})

	// 添加时间范围过滤
	if !startTime.IsZero() && !endTime.IsZero() {
		query = query.Where("fetch_time >= ? AND fetch_time <= ?", startTime, endTime)
	} else if !startTime.IsZero() {
		query = query.Where("fetch_time >= ?", startTime)
	} else if !endTime.IsZero() {
		query = query.Where("fetch_time <= ?", endTime)
	}

	// 添加特定文件ID过滤
	if params.ConfigFileID != "" {
		query = query.Where("id = ?", params.ConfigFileID)
	}

	// 添加文件类型过滤
	if params.FileType != "" {
		query = query.Where("file_type = ?", params.FileType)
	}

	// 添加状态过滤
	if params.Status != "" {
		query = query.Where("status = ?", params.Status)
	}

	// 添加批准状态过滤
	if params.ApprovalStatus != "" {
		query = query.Where("approval_status = ?", params.ApprovalStatus)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("查询配置文件总数失败: %w", err)
	}

	// 分页查询
	offset := (params.Page - 1) * params.PageSize
	var configFiles []model.ConfigFile
	if err := query.Order("id desc").Limit(params.PageSize).Offset(offset).Find(&configFiles).Error; err != nil {
		return nil, fmt.Errorf("查询配置文件失败: %w", err)
	}

	// 转换为响应格式
	var data []model.ConfigFileWithInfo
	statusDistribution := make(map[string]int)
	contentSources := make(map[string]int)

	for _, configFile := range configFiles {
		fileInfo := model.ConfigFileWithInfo{
			ID:             configFile.ID,
			FileName:       configFile.Filename,
			FileType:       configFile.FileType,
			FileSize:       len(configFile.Content),
			Status:         configFile.Status,
			ApprovalStatus: configFile.ApprovalStatus,
			FetchTime:      configFile.FetchTime,
			ProcessTime:    configFile.ProcessTime,
			ApprovalBy:     configFile.ApprovalBy,
			ApprovalTime:   configFile.ApprovalTime,
			CreatedAt:      configFile.CreatedAt,
			UpdatedAt:      configFile.UpdatedAt,
		}

		// 确定整体状态
		overallStatus := m.determineConfigOverallStatus(&configFile)
		fileInfo.OverallStatus = overallStatus
		statusDistribution[overallStatus]++

		// 获取文件路径信息
		fileInfo.FilePaths = m.getFilePathInfo(configFile.Filename)

		// 包含内容（如果需要）
		if params.IncludeContent {
			contentResult := m.getConfigFileContent(&configFile, params.ContentSource)
			fileInfo.Content = contentResult.Content
			fileInfo.ContentInfo = &model.ContentInfo{
				Source:         m.contentSourceToString(contentResult.Source),
				Available:      contentResult.Available,
				Size:           contentResult.Size,
				Encoding:       "utf-8",
				FallbackReason: contentResult.FallbackReason,
			}
			contentSources[fileInfo.ContentInfo.Source]++
		}

		// 包含处理信息
		if !params.IncludeProcessingDetails {
			// 基础模式：简单的处理信息
			processingInfo := m.buildBasicProcessingInfo(&configFile)
			fileInfo.ProcessingInfo = processingInfo
		} else {
			// 详细模式：完整的处理结果
			fileInfo.ProcessingResults = m.buildConfigProcessingResults(&configFile)
		}

		data = append(data, fileInfo)
	}

	// 构建响应
	totalPages := int((total + int64(params.PageSize) - 1) / int64(params.PageSize))

	response := &model.ConfigQueryResponse{
		Code:    200,
		Message: "获取配置文件列表成功",
		Data:    data,
		Pagination: model.ConfigPaginationInfo{
			Page:       params.Page,
			PageSize:   params.PageSize,
			Total:      int(total),
			TotalPages: totalPages,
		},
		Summary: model.ConfigSummaryInfo{
			TotalFiles:         int(total),
			ContentSources:     contentSources,
			StatusDistribution: statusDistribution,
		},
	}

	return response, nil
}

// determineConfigOverallStatus 确定配置文件的整体状态
func (m *Manager) determineConfigOverallStatus(configFile *model.ConfigFile) string {
	// 优先级规则：
	// 1. 如果 approval_status == "pending_approval" → 返回 "pending_approval"
	// 2. 如果 approval_status == "rejected" → 返回 "rejected"
	// 3. 如果 approval_status == "approved" → 根据 status 字段返回对应状态
	// 4. 如果 approval_status == "none" → 根据 status 字段返回对应状态

	if configFile.ApprovalStatus == "pending_approval" {
		return "pending_approval"
	} else if configFile.ApprovalStatus == "rejected" {
		return "rejected"
	} else if configFile.ApprovalStatus == "approved" {
		// 已批准的配置文件，根据执行状态返回
		switch configFile.Status {
		case "success":
			return "success"
		case "processing":
			return "processing"
		case "failed":
			return "failed"
		case "pending":
			return "processing" // 已批准但还在处理中
		default:
			return "processing" // 默认为处理中
		}
	} else if configFile.ApprovalStatus == "none" || configFile.ApprovalStatus == "" {
		// 无需批准的配置文件，直接根据执行状态返回
		switch configFile.Status {
		case "success":
			return "success"
		case "processing":
			return "processing"
		case "failed":
			return "failed"
		case "pending":
			return "pending"
		default:
			return "pending" // 默认为待处理
		}
	} else {
		// 未知的批准状态
		return "unknown"
	}
}

// getFilePathInfo 获取文件路径信息
func (m *Manager) getFilePathInfo(filename string) *model.FilePathsInfo {
	downloadsPath := filepath.Join(m.cfg.ConfigManager.ConfigFileManager.DownloadDir, filename)
	decryptedPath := filepath.Join(m.cfg.ConfigManager.ConfigFileManager.DecryptedDir, filename)

	// 检查文件是否存在
	_, downloadsErr := os.Stat(downloadsPath)
	_, decryptedErr := os.Stat(decryptedPath)

	return &model.FilePathsInfo{
		DownloadsPath:   downloadsPath,
		DecryptedPath:   decryptedPath,
		DownloadsExists: downloadsErr == nil,
		DecryptedExists: decryptedErr == nil,
	}
}

// getConfigFileContent 获取配置文件内容，支持多层级内容获取策略
func (m *Manager) getConfigFileContent(configFile *model.ConfigFile, contentSource string) *model.ContentResult {
	result := &model.ContentResult{
		FileID:   configFile.ID,
		FileName: configFile.Filename,
		Content:  make(map[string]interface{}),
	}

	// 根据内容源偏好决定获取策略
	switch contentSource {
	case "database":
		return m.getContentFromDatabase(configFile, result)
	case "decrypted":
		return m.getContentFromDecrypted(configFile, result)
	case "downloads":
		return m.getContentFromDownloads(configFile, result)
	case "auto":
		fallthrough
	default:
		// 自动模式：数据库 -> decrypted -> downloads
		if dbResult := m.getContentFromDatabase(configFile, result); dbResult.Available {
			return dbResult
		}
		if decResult := m.getContentFromDecrypted(configFile, result); decResult.Available {
			return decResult
		}
		return m.getContentFromDownloads(configFile, result)
	}
}

// getContentFromDatabase 从数据库获取内容
func (m *Manager) getContentFromDatabase(configFile *model.ConfigFile, result *model.ContentResult) *model.ContentResult {
	result.Source = model.SourceDatabase

	if len(configFile.Content) > 0 {
		// 尝试解析JSON内容
		var content map[string]interface{}
		if err := json.Unmarshal(configFile.Content, &content); err != nil {
			// 如果不是JSON，尝试解密后作为纯文本处理
			decryptedContent, decryptErr := m.processor.DecryptFile(configFile)
			if decryptErr != nil {
				// 解密失败，使用原始内容并记录警告
				if m.cfg.DebugLevel == "true" {
					log.Printf("解密配置文件失败，使用原始内容: %s, 错误: %v", configFile.Filename, decryptErr)
				}
				result.Content = map[string]interface{}{
					"raw_content":    string(configFile.Content),
					"content_type":   "text",
					"decrypt_status": "failed",
					"decrypt_error":  decryptErr.Error(),
				}
			} else {
				// 解密成功，使用解密后的内容
				if m.cfg.DebugLevel == "true" {
					log.Printf("成功解密配置文件: %s", configFile.Filename)
				}
				result.Content = map[string]interface{}{
					"raw_content":    string(decryptedContent),
					"content_type":   "text",
					"decrypt_status": "success",
				}
			}
		} else {
			result.Content = content
		}
		result.Available = true
		result.Size = len(configFile.Content)
	} else {
		result.Available = false
		result.FallbackReason = "数据库中无内容记录"
	}

	return result
}

// getContentFromDecrypted 从decrypted目录获取内容
func (m *Manager) getContentFromDecrypted(configFile *model.ConfigFile, result *model.ContentResult) *model.ContentResult {
	result.Source = model.SourceDecrypted

	decryptedPath := filepath.Join(m.cfg.ConfigManager.ConfigFileManager.DecryptedDir, configFile.Filename)
	content, err := m.readFileContent(decryptedPath)
	if err != nil {
		result.Available = false
		result.FallbackReason = fmt.Sprintf("读取decrypted文件失败: %v", err)
		return result
	}

	result.Content = content
	result.Available = true
	result.Size = len(fmt.Sprintf("%v", content))
	return result
}

// getContentFromDownloads 从downloads目录获取内容
func (m *Manager) getContentFromDownloads(configFile *model.ConfigFile, result *model.ContentResult) *model.ContentResult {
	result.Source = model.SourceDownloads

	downloadsPath := filepath.Join(m.cfg.ConfigManager.ConfigFileManager.DownloadDir, configFile.Filename)
	content, err := m.readFileContent(downloadsPath)
	if err != nil {
		result.Available = false
		result.FallbackReason = fmt.Sprintf("读取downloads文件失败: %v", err)
		result.Source = model.SourceUnavailable
		return result
	}

	result.Content = content
	result.Available = true
	result.Size = len(fmt.Sprintf("%v", content))
	return result
}

// readFileContent 读取文件内容并解析为JSON
func (m *Manager) readFileContent(filePath string) (map[string]interface{}, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return nil, err
	}

	var content map[string]interface{}
	if err := json.Unmarshal(data, &content); err != nil {
		// 如果不是JSON，作为纯文本处理
		return map[string]interface{}{
			"raw_content":  string(data),
			"content_type": "text",
		}, nil
	}

	return content, nil
}

// contentSourceToString 将内容源枚举转换为字符串
func (m *Manager) contentSourceToString(source model.ContentSource) string {
	switch source {
	case model.SourceDatabase:
		return "database"
	case model.SourceDecrypted:
		return "decrypted"
	case model.SourceDownloads:
		return "downloads"
	case model.SourceUnavailable:
		return "unavailable"
	default:
		return "unknown"
	}
}

// buildConfigProcessingResults 构建详细的配置文件处理结果信息
func (m *Manager) buildConfigProcessingResults(configFile *model.ConfigFile) *model.ConfigProcessingResultsInfo {
	results := &model.ConfigProcessingResultsInfo{}

	// 解密结果（假设文件存在就是解密成功）
	if len(configFile.Content) > 0 {
		results.DecryptionResult = &model.ConfigProcessingStepResult{
			Status:    "success",
			Timestamp: configFile.CreatedAt,
			Message:   "配置文件解密成功",
			Details: map[string]interface{}{
				"content_size": len(configFile.Content),
				"file_type":    configFile.FileType,
			},
		}
	} else {
		results.DecryptionResult = &model.ConfigProcessingStepResult{
			Status:    "pending",
			Timestamp: configFile.CreatedAt,
			Message:   "等待解密处理",
			Details:   map[string]interface{}{},
		}
	}

	// 解析结果（基于文件状态判断）
	if configFile.Status != "pending" {
		status := "success"
		message := "配置文件解析成功"
		if configFile.Status == "failed" {
			status = "failed"
			message = "配置文件解析失败"
		}

		results.ParsingResult = &model.ConfigProcessingStepResult{
			Status:    status,
			Timestamp: configFile.UpdatedAt,
			Message:   message,
			Details: map[string]interface{}{
				"file_type": configFile.FileType,
				"status":    configFile.Status,
			},
		}
	} else {
		results.ParsingResult = &model.ConfigProcessingStepResult{
			Status:    "pending",
			Timestamp: configFile.CreatedAt,
			Message:   "等待解析处理",
			Details:   map[string]interface{}{},
		}
	}

	// 执行结果
	if configFile.ProcessTime != nil {
		status := configFile.Status
		message := fmt.Sprintf("配置文件执行%s", status)

		details := map[string]interface{}{
			"execution_status": status,
			"approval_status":  configFile.ApprovalStatus,
		}

		if configFile.Result != "" {
			details["result_summary"] = configFile.Result[:min(100, len(configFile.Result))] + "..."
		}

		results.ExecutionResult = &model.ConfigProcessingStepResult{
			Status:    status,
			Timestamp: *configFile.ProcessTime,
			Message:   message,
			Details:   details,
		}
	} else {
		results.ExecutionResult = &model.ConfigProcessingStepResult{
			Status:    "pending",
			Timestamp: configFile.CreatedAt,
			Message:   "等待执行处理",
			Details:   map[string]interface{}{},
		}
	}

	return results
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// BuildBasicProcessingInfo 构建基础的配置文件处理信息（实现ProcessingInfoProvider接口）
func (m *Manager) BuildBasicProcessingInfo(configFile *model.ConfigFile) *model.ConfigProcessingInfo {
	return m.buildBasicProcessingInfo(configFile)
}

// buildBasicProcessingInfo 构建基础的配置文件处理信息
func (m *Manager) buildBasicProcessingInfo(configFile *model.ConfigFile) *model.ConfigProcessingInfo {
	// 简化 result 字段，提取关键信息
	simplifiedResult := m.simplifyResultMessage(configFile.Result)

	processingInfo := &model.ConfigProcessingInfo{
		Result:       simplifiedResult,
		IsDecrypted:  len(configFile.Content) > 0,   // 有内容表示已解密
		IsExecuted:   configFile.ProcessTime != nil, // 有处理时间表示已执行
		SuccessList:  []model.ProcessingEntity{},
		FailList:     []model.ProcessingFailEntity{},
		ApprovalBy:   configFile.ApprovalBy,
		ApprovalTime: configFile.ApprovalTime,
	}

	// 如果配置文件已执行，尝试从执行结果中提取成功和失败的实体列表
	if processingInfo.IsExecuted {
		successList, failList := m.extractEntityListsFromResult(configFile)
		processingInfo.SuccessList = successList
		processingInfo.FailList = failList
	}

	return processingInfo
}

// extractEntityListsFromResult 从执行结果中提取成功和失败的实体列表
func (m *Manager) extractEntityListsFromResult(configFile *model.ConfigFile) ([]model.ProcessingEntity, []model.ProcessingFailEntity) {
	var successList []model.ProcessingEntity
	var failList []model.ProcessingFailEntity

	// 首先尝试从 result 字段解析 JSON 数据
	if configFile.Result != "" {
		successList, failList = m.parseResultJSON(configFile.Result)
		if len(successList) > 0 || len(failList) > 0 {
			return successList, failList
		}
	}

	// 如果 result 字段解析失败，尝试从数据库中获取详细的执行结果
	if executionResult, err := m.GetResult(configFile.ID); err == nil && executionResult != nil {
		// 遍历执行结果中的操作详情
		for _, operation := range executionResult.Details.Operations {
			entityType := m.mapItemTypeToEntityType(operation.ItemResult.Type)
			entityID := operation.ItemResult.BusinessID

			if operation.Status == "success" {
				// 成功的实体
				successList = append(successList, model.ProcessingEntity{
					EntityID:   entityID,
					EntityType: entityType,
				})
			} else {
				// 失败的实体
				failReason := operation.Message
				if failReason == "" {
					failReason = "执行失败"
				}
				failList = append(failList, model.ProcessingFailEntity{
					EntityID:   entityID,
					EntityType: entityType,
					FailReason: failReason,
				})
			}
		}
	} else {
		// 如果无法获取详细执行结果，尝试从配置文件内容中推断
		if len(configFile.Content) > 0 {
			successList, failList = m.extractEntityListsFromContent(configFile)
		}
	}

	return successList, failList
}

// extractEntityListsFromContent 从配置文件内容中提取实体列表（备用方法）
func (m *Manager) extractEntityListsFromContent(configFile *model.ConfigFile) ([]model.ProcessingEntity, []model.ProcessingFailEntity) {
	var successList []model.ProcessingEntity
	var failList []model.ProcessingFailEntity

	// 解析配置文件内容
	var content map[string]interface{}
	if err := json.Unmarshal(configFile.Content, &content); err != nil {
		return successList, failList
	}

	// 根据文件类型处理不同的内容结构
	switch configFile.FileType {
	case "terminal", "del_terminal":
		if terminals, ok := content["terminal"].([]interface{}); ok {
			for _, terminalInterface := range terminals {
				if terminal, ok := terminalInterface.(map[string]interface{}); ok {
					if businessID, exists := terminal["businessid"].(string); exists {
						// 如果配置文件状态是成功，假设所有实体都成功
						if configFile.Status == "success" {
							successList = append(successList, model.ProcessingEntity{
								EntityID:   businessID,
								EntityType: "user",
							})
						} else if configFile.Status == "failed" {
							failList = append(failList, model.ProcessingFailEntity{
								EntityID:   businessID,
								EntityType: "user",
								FailReason: configFile.Result,
							})
						}
					}
				}
			}
		}
	case "gateway", "del_gateway":
		// 处理网关配置
		if businessID, exists := content["businessid"].(string); exists {
			if configFile.Status == "success" {
				successList = append(successList, model.ProcessingEntity{
					EntityID:   businessID,
					EntityType: "gateway",
				})
			} else if configFile.Status == "failed" {
				failList = append(failList, model.ProcessingFailEntity{
					EntityID:   businessID,
					EntityType: "gateway",
					FailReason: configFile.Result,
				})
			}
		}
	case "terminal_connect":
		// 处理终端连接配置
		if terminals, ok := content["terminal_connect"].([]interface{}); ok {
			for _, terminalInterface := range terminals {
				if terminal, ok := terminalInterface.(map[string]interface{}); ok {
					if preID, exists := terminal["preid"].(string); exists {
						if configFile.Status == "success" {
							successList = append(successList, model.ProcessingEntity{
								EntityID:   preID,
								EntityType: "user",
							})
						} else if configFile.Status == "failed" {
							failList = append(failList, model.ProcessingFailEntity{
								EntityID:   preID,
								EntityType: "user",
								FailReason: configFile.Result,
							})
						}
					}
				}
			}
		}
	case "batch":
		// 处理批量配置，需要解析更复杂的结构
		successList, failList = m.extractEntityListsFromBatchContent(content, configFile)
	}

	return successList, failList
}

// extractEntityListsFromBatchContent 从批量配置内容中提取实体列表
func (m *Manager) extractEntityListsFromBatchContent(content map[string]interface{}, configFile *model.ConfigFile) ([]model.ProcessingEntity, []model.ProcessingFailEntity) {
	var successList []model.ProcessingEntity
	var failList []model.ProcessingFailEntity

	// 批量配置可能包含多种类型的操作
	for key, value := range content {
		switch key {
		case "terminal":
			if terminals, ok := value.([]interface{}); ok {
				for _, terminalInterface := range terminals {
					if terminal, ok := terminalInterface.(map[string]interface{}); ok {
						if businessID, exists := terminal["businessid"].(string); exists {
							if configFile.Status == "success" {
								successList = append(successList, model.ProcessingEntity{
									EntityID:   businessID,
									EntityType: "user",
								})
							} else if configFile.Status == "failed" {
								failList = append(failList, model.ProcessingFailEntity{
									EntityID:   businessID,
									EntityType: "user",
									FailReason: configFile.Result,
								})
							}
						}
					}
				}
			}
		case "gateway":
			if gateways, ok := value.([]interface{}); ok {
				for _, gatewayInterface := range gateways {
					if gateway, ok := gatewayInterface.(map[string]interface{}); ok {
						if businessID, exists := gateway["businessid"].(string); exists {
							// 根据设备ID查询数据库获取实际的设备类型
							entityType := m.getEntityTypeByDeviceID(businessID)

							if configFile.Status == "success" {
								successList = append(successList, model.ProcessingEntity{
									EntityID:   businessID,
									EntityType: entityType,
								})
							} else if configFile.Status == "failed" {
								failList = append(failList, model.ProcessingFailEntity{
									EntityID:   businessID,
									EntityType: entityType,
									FailReason: configFile.Result,
								})
							}
						}
					}
				}
			}
		}
	}

	return successList, failList
}

// getEntityTypeByDeviceID 根据设备ID查询数据库获取实际的设备类型
func (m *Manager) getEntityTypeByDeviceID(deviceID string) string {
	// 获取数据库连接
	db, err := database.GetDB()
	if err != nil {
		if m.cfg.DebugLevel == "true" {
			log.Printf("获取数据库连接失败，使用默认entity_type: %v", err)
		}
		return "gateway" // 默认返回gateway
	}

	// 查询设备信息
	var device models.Device
	if err := db.Select("device_type").Where("device_id = ?", deviceID).First(&device).Error; err != nil {
		if m.cfg.DebugLevel == "true" {
			log.Printf("查询设备 %s 失败，使用默认entity_type: %v", deviceID, err)
		}
		return "gateway" // 默认返回gateway
	}

	// 根据设备类型映射entity_type
	switch device.DeviceType {
	case 1, 2, 3:
		return "gateway" // 网关设备
	case 4:
		return "software" // 安全接入管理设备
	default:
		if m.cfg.DebugLevel == "true" {
			log.Printf("未知的设备类型 %d，设备ID: %s", device.DeviceType, deviceID)
		}
		return "gateway" // 默认返回gateway
	}
}

// mapItemTypeToEntityType 将项目类型映射为实体类型
func (m *Manager) mapItemTypeToEntityType(itemType string) string {
	switch itemType {
	case "terminal":
		return "user"
	case "gateway":
		return "gateway"
	case "terminal_connect":
		return "user"
	case "software":
		return "software"
	default:
		return "unknown"
	}
}

// parseResultJSON 解析 result 字段中的 JSON 数据，提取成功和失败的实体列表
func (m *Manager) parseResultJSON(resultJSON string) ([]model.ProcessingEntity, []model.ProcessingFailEntity) {
	var successList []model.ProcessingEntity
	var failList []model.ProcessingFailEntity

	// 尝试解析 result 字段中的 JSON 数据
	var executionResult model.ExecutionResult
	if err := json.Unmarshal([]byte(resultJSON), &executionResult); err != nil {
		if m.cfg.DebugLevel == "true" {
			log.Printf("解析 result JSON 失败: %v", err)
		}
		return successList, failList
	}

	// 遍历执行结果中的操作详情
	for _, operation := range executionResult.Details.Operations {
		entityID := operation.ItemResult.BusinessID

		// 对于gateway类型，需要根据数据库中的实际设备类型来确定entity_type
		var entityType string
		if operation.ItemResult.Type == "gateway" {
			entityType = m.getEntityTypeByDeviceID(entityID)
		} else {
			entityType = m.mapItemTypeToEntityType(operation.ItemResult.Type)
		}

		if operation.Status == "success" {
			// 成功的实体
			successList = append(successList, model.ProcessingEntity{
				EntityID:   entityID,
				EntityType: entityType,
			})
		} else {
			// 失败的实体
			failReason := operation.Message
			if failReason == "" {
				failReason = "执行失败"
			}
			failList = append(failList, model.ProcessingFailEntity{
				EntityID:   entityID,
				EntityType: entityType,
				FailReason: failReason,
			})
		}
	}

	return successList, failList
}

// simplifyResultMessage 简化 result 消息，提取关键信息
func (m *Manager) simplifyResultMessage(resultJSON string) string {
	if resultJSON == "" {
		return "无执行结果"
	}

	// 尝试解析 JSON 并提取关键信息
	var executionResult model.ExecutionResult
	if err := json.Unmarshal([]byte(resultJSON), &executionResult); err != nil {
		// 如果解析失败，返回原始字符串的前100个字符
		if len(resultJSON) > 100 {
			return resultJSON[:100] + "..."
		}
		return resultJSON
	}

	// 构建简化的结果消息
	if executionResult.Message != "" {
		return executionResult.Message
	}

	// 如果没有消息，根据状态和汇总信息构建消息
	if executionResult.Status == "success" {
		if executionResult.Summary.TotalItems > 0 {
			return fmt.Sprintf("执行成功，共处理 %d 个项目，成功 %d 个",
				executionResult.Summary.TotalItems,
				executionResult.Summary.SuccessItems)
		}
		return "执行成功"
	} else if executionResult.Status == "failed" {
		if executionResult.Summary.TotalItems > 0 {
			return fmt.Sprintf("执行失败，共处理 %d 个项目，失败 %d 个",
				executionResult.Summary.TotalItems,
				executionResult.Summary.FailedItems)
		}
		return "执行失败"
	} else if executionResult.Status == "partial" {
		return fmt.Sprintf("部分成功，共处理 %d 个项目，成功 %d 个，失败 %d 个",
			executionResult.Summary.TotalItems,
			executionResult.Summary.SuccessItems,
			executionResult.Summary.FailedItems)
	}

	return fmt.Sprintf("状态: %s", executionResult.Status)
}
