package transfer

import (
	"bytes"
	"crypto/tls"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"gin-server/config"
)

// GitlabTransporter GitLab文件传输实现
type GitlabTransporter struct {
	accessToken string
	host        string
	owner       string
	repo        string
	branch      string
	client      *http.Client
}

// GitlabFile GitLab文件信息
type GitlabFile struct {
	Path       string `json:"path"`
	Type       string `json:"type"`
	Size       int64  `json:"size"`
	Name       string `json:"name"`
	ID         string `json:"id"`
	BlobID     string `json:"blob_id"`
	Content    string `json:"content"`
	Encoding   string `json:"encoding"`
	LastCommit struct {
		ID        string    `json:"id"`
		CreatedAt time.Time `json:"created_at"`
	} `json:"last_commit"`
}

// NewGitlabTransporter 创建GitLab传输器
func NewGitlabTransporter(cfg *config.GitlabConfig) *GitlabTransporter {
	host := cfg.Host
	if host == "" {
		host = "https://gitlab.com"
	} else {
		// 确保host不以斜杠结尾
		host = strings.TrimSuffix(host, "/")
	}

	// 创建HTTP客户端，支持TLS配置
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 检查是否需要跳过TLS验证（用于Docker容器环境）
	globalCfg := config.GetConfig()
	if globalCfg != nil && globalCfg.SkipTLSVerify {
		transport := &http.Transport{
			TLSClientConfig: &tls.Config{
				InsecureSkipVerify: true,
			},
		}
		client.Transport = transport
		if globalCfg.DebugLevel == "true" {
			log.Println("警告: 已跳过TLS证书验证，这在生产环境中不安全")
		}
	}

	return &GitlabTransporter{
		accessToken: cfg.AccessToken,
		host:        host,
		owner:       cfg.Owner,
		repo:        cfg.Repo,
		branch:      cfg.Branch,
		client:      client,
	}
}

// Upload 上传文件到GitLab
func (t *GitlabTransporter) Upload(localPath, remotePath string) error {
	cfg := config.GetConfig()

	// 检查本地文件是否存在
	if _, err := os.Stat(localPath); os.IsNotExist(err) {
		return NewTransferError("upload", remotePath, fmt.Errorf("本地文件不存在: %s", localPath))
	}

	// 读取本地文件内容
	content, err := os.ReadFile(localPath)
	if err != nil {
		return NewTransferError("upload", remotePath, fmt.Errorf("读取本地文件失败: %w", err))
	}

	// 构造API URL
	// 确保远程路径格式正确
	if !strings.HasPrefix(remotePath, "/") && remotePath != "" {
		remotePath = "/" + remotePath
	}

	// 对文件路径进行编码
	pathParts := strings.Split(strings.TrimPrefix(remotePath, "/"), "/")
	encodedPathParts := make([]string, len(pathParts))
	for i, part := range pathParts {
		encodedPathParts[i] = url.PathEscape(part)
	}
	encodedPath := strings.Join(encodedPathParts, "%2F")

	// 使用owner/repo格式构建项目标识符
	projectPath := url.PathEscape(t.owner) + "%2F" + url.PathEscape(t.repo)

	apiURL := fmt.Sprintf("%s/api/v4/projects/%s/repository/files/%s",
		strings.TrimSuffix(t.host, "/"),
		projectPath,
		encodedPath)

	if cfg.DebugLevel == "true" {
		log.Printf("API URL: %s\n", apiURL)
	}

	// 检查文件是否为JSON文件，如果是JSON文件，使用text/plain编码而不是修改内容
	var fileContent string
	var isJson bool
	if strings.HasSuffix(strings.ToLower(remotePath), ".json") {
		isJson = true
		// 对于JSON文件，使用base64编码但保留原始编码
		fileContent = base64.StdEncoding.EncodeToString(content)
	} else {
		// 非JSON文件，使用标准的base64编码
		fileContent = base64.StdEncoding.EncodeToString(content)
	}

	// 构造请求体
	reqBody := map[string]interface{}{
		"branch":         t.branch,
		"content":        fileContent,
		"commit_message": fmt.Sprintf("Upload file %s", remotePath),
		"encoding":       "base64",
	}

	// 构造请求
	reqBytes, err := json.Marshal(reqBody)
	if err != nil {
		return NewTransferError("upload", remotePath, fmt.Errorf("序列化请求体失败: %w", err))
	}

	req, err := http.NewRequest("POST", apiURL, bytes.NewBuffer(reqBytes))
	if err != nil {
		return NewTransferError("upload", remotePath, fmt.Errorf("创建HTTP请求失败: %w", err))
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("PRIVATE-TOKEN", t.accessToken)

	// 对于JSON文件，确保服务器理解我们使用的是base64编码，但内容是原始格式
	if isJson {
		req.Header.Set("X-GitLab-File-Raw-Content", "true")
	}

	// 执行请求
	resp, err := t.client.Do(req)
	if err != nil {
		return NewTransferError("upload", remotePath, fmt.Errorf("发送HTTP请求失败: %w", err))
	}
	defer resp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return NewTransferError("upload", remotePath, fmt.Errorf("读取响应内容失败: %w", err))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("响应状态码: %d\n", resp.StatusCode)
		log.Printf("响应内容: %s\n", string(respBody))
	}

	if resp.StatusCode < 200 || resp.StatusCode >= 300 {
		return NewTransferError("upload", remotePath, fmt.Errorf("上传文件失败: HTTP %d - %s", resp.StatusCode, resp.Status))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("成功上传文件 %s 到 %s，文件大小: %d 字节\n", localPath, remotePath, len(content))
	}

	return nil
}

// Download 从GitLab下载文件
func (t *GitlabTransporter) Download(remotePath, localPath string) error {
	cfg := config.GetConfig()

	// 获取文件内容
	file, err := t.getFile(remotePath)
	if err != nil {
		return NewTransferError("download", remotePath, fmt.Errorf("获取文件内容失败: %w", err))
	}

	if file == nil {
		return NewTransferError("download", remotePath, fmt.Errorf("文件不存在"))
	}

	// 解码文件内容
	var content []byte
	isJsonFile := strings.HasSuffix(strings.ToLower(remotePath), ".json")

	// GitLab API返回的文件内容通常是base64编码的
	if file.Encoding == "base64" {
		content, err = base64.StdEncoding.DecodeString(file.Content)
		if err != nil {
			return NewTransferError("download", remotePath, fmt.Errorf("解码文件内容失败: %w", err))
		}

		// 对于JSON文件，确保内容保持原样，不进行任何额外处理
		if isJsonFile && cfg.DebugLevel == "true" {
			log.Printf("下载JSON文件 %s，保持原始编码，文件大小: %d 字节\n",
				remotePath, len(content))
		}
	} else {
		content = []byte(file.Content)
	}

	// 确保目标目录存在
	dir := filepath.Dir(localPath)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return NewTransferError("download", remotePath, fmt.Errorf("创建目标目录失败: %w", err))
	}

	// 写入本地文件
	if err := os.WriteFile(localPath, content, 0644); err != nil {
		return NewTransferError("download", remotePath, fmt.Errorf("写入本地文件失败: %w", err))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("成功下载文件 %s 到 %s，文件大小: %d 字节\n", remotePath, localPath, len(content))
	}

	return nil
}

// List 列出目录内容
func (t *GitlabTransporter) List(remotePath string) ([]FileInfo, error) {
	cfg := config.GetConfig()

	// 构建API URL
	// 在GitLab中，如果要列出目录内容，需要使用树形API
	urlEncodedPath := ""
	if remotePath != "" && remotePath != "/" {
		// 修复URL编码问题
		pathParts := strings.Split(strings.TrimPrefix(remotePath, "/"), "/")
		for i, part := range pathParts {
			pathParts[i] = url.PathEscape(part)
		}
		urlEncodedPath = fmt.Sprintf("&path=%s", strings.Join(pathParts, "%2F"))
	}

	// 使用owner/repo格式构建项目标识符
	projectPath := url.PathEscape(t.owner) + "%2F" + url.PathEscape(t.repo)

	apiURL := fmt.Sprintf("%s/api/v4/projects/%s/repository/tree?ref=%s%s",
		strings.TrimSuffix(t.host, "/"),
		projectPath,
		url.QueryEscape(t.branch),
		urlEncodedPath)

	if cfg.DebugLevel == "true" {
		log.Printf("API URL: %s\n", apiURL)
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodGet, apiURL, nil)
	if err != nil {
		return nil, NewTransferError("list", remotePath, fmt.Errorf("创建请求失败: %w", err))
	}
	req.Header.Set("PRIVATE-TOKEN", t.accessToken)

	// 发送请求
	resp, err := t.client.Do(req)
	if err != nil {
		return nil, NewTransferError("list", remotePath, fmt.Errorf("发送请求失败: %w", err))
	}
	defer resp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, NewTransferError("list", remotePath, fmt.Errorf("读取响应内容失败: %w", err))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("响应状态码: %d\n", resp.StatusCode)
		log.Printf("响应内容: %s\n", string(respBody))
	}

	if resp.StatusCode == http.StatusNotFound {
		return []FileInfo{}, nil
	}

	if resp.StatusCode != http.StatusOK {
		return nil, NewTransferError("list", remotePath, fmt.Errorf("获取目录列表失败: HTTP %d - %s", resp.StatusCode, resp.Status))
	}

	// 解析响应
	var items []struct {
		ID   string `json:"id"`
		Name string `json:"name"`
		Type string `json:"type"`
		Path string `json:"path"`
	}

	if err := json.Unmarshal(respBody, &items); err != nil {
		return nil, NewTransferError("list", remotePath, fmt.Errorf("解析响应失败: %w", err))
	}

	result := make([]FileInfo, 0, len(items))
	for _, item := range items {
		fi := FileInfo{
			Name:  item.Name,
			Path:  item.Path,
			IsDir: item.Type == "tree",
			Hash:  item.ID,
		}

		// 对于文件，获取更多信息
		if !fi.IsDir {
			fileInfo, err := t.getFile(item.Path)
			if err == nil && fileInfo != nil {
				fi.Size = fileInfo.Size
				if fileInfo.LastCommit.CreatedAt.IsZero() {
					// 如果没有获取到修改时间，使用当前时间
					fi.ModTime = time.Now()
				} else {
					fi.ModTime = fileInfo.LastCommit.CreatedAt
				}
			}
		}

		result = append(result, fi)
	}

	if cfg.DebugLevel == "true" {
		log.Printf("成功列出目录 %s 的内容，共 %d 个项目\n", remotePath, len(result))
	}

	return result, nil
}

// Delete 删除文件
func (t *GitlabTransporter) Delete(remotePath string) error {
	cfg := config.GetConfig()

	// 检查文件是否存在
	_, err := t.getFile(remotePath)
	if err != nil {
		if strings.Contains(err.Error(), "404") {
			// 文件不存在，视为删除成功
			if cfg.DebugLevel == "true" {
				log.Printf("文件 %s 不存在，跳过删除操作\n", remotePath)
			}
			return nil
		}
		return NewTransferError("delete", remotePath, fmt.Errorf("检查文件存在失败: %w", err))
	}

	// 准备请求数据
	// 修复URL编码问题
	pathParts := strings.Split(strings.TrimPrefix(remotePath, "/"), "/")
	encodedPathParts := make([]string, len(pathParts))
	for i, part := range pathParts {
		encodedPathParts[i] = url.PathEscape(part)
	}
	encodedPath := strings.Join(encodedPathParts, "%2F")

	// 使用owner/repo格式构建项目标识符
	projectPath := url.PathEscape(t.owner) + "%2F" + url.PathEscape(t.repo)

	apiURL := fmt.Sprintf("%s/api/v4/projects/%s/repository/files/%s",
		strings.TrimSuffix(t.host, "/"),
		projectPath,
		encodedPath)

	if cfg.DebugLevel == "true" {
		log.Printf("API URL: %s\n", apiURL)
	}

	// 创建请求数据
	reqBody := map[string]interface{}{
		"branch":         t.branch,
		"commit_message": fmt.Sprintf("删除文件: %s", remotePath),
	}

	// 编码请求数据
	reqBytes, err := json.Marshal(reqBody)
	if err != nil {
		return NewTransferError("delete", remotePath, fmt.Errorf("序列化请求体失败: %w", err))
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodDelete, apiURL, bytes.NewBuffer(reqBytes))
	if err != nil {
		return NewTransferError("delete", remotePath, fmt.Errorf("创建HTTP请求失败: %w", err))
	}
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("PRIVATE-TOKEN", t.accessToken)

	// 发送请求
	resp, err := t.client.Do(req)
	if err != nil {
		return NewTransferError("delete", remotePath, fmt.Errorf("发送HTTP请求失败: %w", err))
	}
	defer resp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return NewTransferError("delete", remotePath, fmt.Errorf("读取响应内容失败: %w", err))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("响应状态码: %d\n", resp.StatusCode)
		log.Printf("响应内容: %s\n", string(respBody))
	}

	// 检查响应状态
	if resp.StatusCode != http.StatusNoContent && resp.StatusCode != http.StatusOK {
		return NewTransferError("delete", remotePath, fmt.Errorf("删除文件失败: HTTP %d - %s", resp.StatusCode, resp.Status))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("成功删除文件 %s\n", remotePath)
	}

	return nil
}

// LastModified 获取文件最后修改时间
func (t *GitlabTransporter) LastModified(remotePath string) (time.Time, error) {
	cfg := config.GetConfig()

	// 获取文件信息，包含最后提交时间
	file, err := t.getFile(remotePath)
	if err != nil {
		return time.Time{}, NewTransferError("lastModified", remotePath, err)
	}

	if file == nil {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("文件不存在"))
	}

	// 如果文件信息中包含LastCommit.CreatedAt，直接返回
	if !file.LastCommit.CreatedAt.IsZero() {
		if cfg.DebugLevel == "true" {
			log.Printf("获取到文件 %s 的最后修改时间: %s\n", remotePath, file.LastCommit.CreatedAt)
		}
		return file.LastCommit.CreatedAt, nil
	}

	// 否则需要获取文件的提交历史
	// 修复URL编码问题
	pathParts := strings.Split(strings.TrimPrefix(remotePath, "/"), "/")
	encodedPathParts := make([]string, len(pathParts))
	for i, part := range pathParts {
		encodedPathParts[i] = url.PathEscape(part)
	}
	urlEncodedPath := strings.Join(encodedPathParts, "%2F")

	// 使用owner/repo格式构建项目标识符
	projectPath := url.PathEscape(t.owner) + "%2F" + url.PathEscape(t.repo)

	apiURL := fmt.Sprintf("%s/api/v4/projects/%s/repository/commits?path=%s&ref_name=%s&per_page=1",
		strings.TrimSuffix(t.host, "/"),
		projectPath,
		urlEncodedPath,
		url.QueryEscape(t.branch))

	if cfg.DebugLevel == "true" {
		log.Printf("API URL: %s\n", apiURL)
	}

	// 创建请求
	req, err := http.NewRequest(http.MethodGet, apiURL, nil)
	if err != nil {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("创建HTTP请求失败: %w", err))
	}
	req.Header.Set("PRIVATE-TOKEN", t.accessToken)

	// 发送请求
	resp, err := t.client.Do(req)
	if err != nil {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("发送HTTP请求失败: %w", err))
	}
	defer resp.Body.Close()

	// 读取响应内容
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("读取响应内容失败: %w", err))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("响应状态码: %d\n", resp.StatusCode)
		log.Printf("响应内容: %s\n", string(respBody))
	}

	if resp.StatusCode != http.StatusOK {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("获取提交历史失败: HTTP %d - %s", resp.StatusCode, resp.Status))
	}

	// 解析响应
	var commits []struct {
		CreatedAt time.Time `json:"created_at"`
	}

	if err := json.Unmarshal(respBody, &commits); err != nil {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("解析响应失败: %w", err))
	}

	if len(commits) == 0 {
		return time.Time{}, NewTransferError("lastModified", remotePath, fmt.Errorf("未找到提交历史"))
	}

	if cfg.DebugLevel == "true" {
		log.Printf("获取到文件 %s 的最后修改时间: %s\n", remotePath, commits[0].CreatedAt)
	}

	return commits[0].CreatedAt, nil
}

// Close 关闭传输器
func (t *GitlabTransporter) Close() error {
	// GitLab API 调用不需要特别的关闭操作
	return nil
}

// getFile 获取文件信息
func (t *GitlabTransporter) getFile(path string) (*GitlabFile, error) {
	cfg := config.GetConfig()

	// 修复URL编码问题：确保路径中的斜杠和其他特殊字符被正确编码
	// 先将路径的每个部分分开处理，然后再将它们用正确的%2F连接
	pathParts := strings.Split(strings.TrimPrefix(path, "/"), "/")
	for i, part := range pathParts {
		pathParts[i] = url.PathEscape(part)
	}
	urlEncodedPath := strings.Join(pathParts, "%2F")

	url := fmt.Sprintf("%s/api/v4/projects/%s%%2F%s/repository/files/%s?ref=%s",
		t.host, url.PathEscape(t.owner), url.PathEscape(t.repo), urlEncodedPath, url.QueryEscape(t.branch))

	// 创建请求
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("创建请求失败: %w", err)
	}
	req.Header.Set("PRIVATE-TOKEN", t.accessToken)

	// 发送请求
	resp, err := t.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		return nil, fmt.Errorf("文件不存在: 404 Not Found")
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("获取文件信息失败: HTTP %d - %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var file GitlabFile
	if err := json.NewDecoder(resp.Body).Decode(&file); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	if cfg.DebugLevel == "true" {
		log.Printf("获取到文件信息: 路径=%s, 大小=%d, ID=%s\n", file.Path, file.Size, file.ID)
	}

	return &file, nil
}
