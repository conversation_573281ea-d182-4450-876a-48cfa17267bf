package controller

import (
	"errors"
	"fmt"
	"gin-server/config"
	"gin-server/configmanager/common/transfer"
	"gin-server/configmanager/config_exec/model"
	"gin-server/database"
	"log"
	"os"
	"path/filepath"
	"regexp"
	"strings"
	"time"

	"gorm.io/gorm"
)

// Fetcher 文件获取器
type Fetcher struct {
	cfg         *config.Config
	transporter transfer.FileTransporter
	downloadDir string
	remotePath  string
}

// NewFetcher 创建新的文件获取器
func NewFetcher(cfg *config.Config) (*Fetcher, error) {
	// 从配置获取下载目录，确保目录存在
	downloadDir := cfg.ConfigManager.ConfigFileManager.DownloadDir
	if downloadDir == "" {
		downloadDir = filepath.Join("configmanager", "config_exec", "downloads")
	}
	os.MkdirAll(downloadDir, 0755)

	// 获取远程路径
	remotePath := cfg.ConfigManager.ConfigFileManager.RemotePath
	if remotePath == "" {
		remotePath = "configs"
	}

	// 创建文件传输器，优先使用ConfigFileManager的专用Storage配置
	var transporter transfer.FileTransporter
	var err error

	if cfg.ConfigManager.ConfigFileManager.Storage != nil {
		// 使用配置文件管理专用的Storage配置
		transporterType := transfer.TransporterType(cfg.ConfigManager.ConfigFileManager.Storage.Type)
		transporter, err = createTransporter(transporterType, cfg)
	} else {
		// 回退到全局Storage配置
		transporterType := transfer.TransporterType(cfg.ConfigManager.Storage.Type)
		transporter, err = transfer.NewFileTransporter(transporterType, cfg)
	}

	if err != nil {
		return nil, fmt.Errorf("创建文件传输器失败: %w", err)
	}

	fetcher := &Fetcher{
		cfg:         cfg,
		transporter: transporter,
		downloadDir: downloadDir,
		remotePath:  remotePath,
	}

	// 记录初始化信息
	log.Printf("[INFO] Fetcher初始化完成")
	log.Printf("[INFO] 下载目录: %s", downloadDir)
	log.Printf("[INFO] 远程路径: %s", remotePath)
	log.Printf("[INFO] 时间戳文件: %s", cfg.ConfigManager.ConfigFileManager.TimestampFile)
	log.Printf("[INFO] 初始时间戳: %s", cfg.ConfigManager.ConfigFileManager.InitialTimestamp)
	log.Printf("[INFO] 增量下载机制已启用")

	return fetcher, nil
}

// createTransporter 根据ConfigFileManager的Storage配置创建传输器
func createTransporter(typ transfer.TransporterType, cfg *config.Config) (transfer.FileTransporter, error) {
	switch typ {
	case transfer.TransporterTypeGitee:
		if cfg.ConfigManager.ConfigFileManager.Storage.Gitee.AccessToken == "" {
			// 使用全局Gitee配置
			if cfg.Gitee == nil {
				return nil, fmt.Errorf("Gitee配置为空")
			}
			return transfer.NewGiteeTransporter(cfg.Gitee), nil
		}
		// 使用ConfigFileManager的Gitee配置
		return transfer.NewGiteeTransporter(&cfg.ConfigManager.ConfigFileManager.Storage.Gitee), nil
	case transfer.TransporterTypeGitlab:
		if cfg.ConfigManager.ConfigFileManager.Storage.Gitlab.AccessToken == "" {
			// 使用全局Gitlab配置
			if cfg.Gitlab == nil {
				return nil, fmt.Errorf("Gitlab配置为空")
			}
			return transfer.NewGitlabTransporter(cfg.Gitlab), nil
		}
		// 使用ConfigFileManager的Gitlab配置
		return transfer.NewGitlabTransporter(&cfg.ConfigManager.ConfigFileManager.Storage.Gitlab), nil
	case transfer.TransporterTypeFTP:
		if cfg.ConfigManager.ConfigFileManager.Storage.FTP.Host == "" {
			// 使用全局FTP配置
			if cfg.FTP == nil {
				return nil, fmt.Errorf("FTP配置为空")
			}
			transporter, err := transfer.NewFTPTransporter(cfg.FTP)
			if err != nil {
				return nil, fmt.Errorf("创建FTP传输器失败: %w", err)
			}
			return transporter, nil
		}
		// 使用ConfigFileManager的FTP配置
		transporter, err := transfer.NewFTPTransporter(&cfg.ConfigManager.ConfigFileManager.Storage.FTP)
		if err != nil {
			return nil, fmt.Errorf("创建FTP传输器失败: %w", err)
		}
		return transporter, nil
	default:
		return nil, fmt.Errorf("不支持的传输器类型: %s", typ)
	}
}

// FetchConfigFiles 获取配置文件
func (f *Fetcher) FetchConfigFiles() ([]*model.ConfigFile, error) {
	log.Println("开始获取配置文件...")

	// 获取远程配置目录下的所有文件
	dirPath := f.cfg.ConfigManager.ConfigFileManager.RemotePath
	log.Printf("获取远程目录: %s\n", dirPath)

	// 如果在测试模式下，从本地测试目录读取文件
	if f.cfg.TestMode {
		log.Println("测试模式，从本地测试目录读取文件")
		supportedTypes := f.cfg.ConfigManager.ConfigFileManager.FileTypes
		return f.fetchConfigFilesFromLocal(supportedTypes)
	}

	// 从远程目录获取配置文件
	supportedTypes := f.cfg.ConfigManager.ConfigFileManager.FileTypes
	configFiles, err := f.getConfigFilesFromDir(dirPath, supportedTypes)
	if err != nil {
		log.Printf("获取远程配置文件失败: %v\n", err)
		return nil, err
	}

	log.Printf("获取到 %d 个配置文件\n", len(configFiles))

	// 不再在这里删除远程文件，将这一步留给调用者执行
	// 这样调用者可以先检查文件是否已存在于数据库中

	return configFiles, nil
}

// fetchConfigFilesFromLocal 从本地测试目录获取配置文件
func (f *Fetcher) fetchConfigFilesFromLocal(supportedTypes []string) ([]*model.ConfigFile, error) {
	testDir := "test"
	if f.cfg.ConfigManager.ConfigFileManager.TestDir != "" {
		testDir = f.cfg.ConfigManager.ConfigFileManager.TestDir
	}

	var configFiles []*model.ConfigFile

	// 获取test目录下的所有子目录
	testDirs, err := os.ReadDir(testDir)
	if err != nil {
		log.Printf("读取测试目录失败: %v", err)
		return nil, fmt.Errorf("读取测试目录失败: %w", err)
	}

	// 首先处理test目录下的文件
	dirFiles, err := f.getLocalConfigFilesFromDir(testDir, supportedTypes)
	if err != nil {
		log.Printf("从测试目录获取配置文件失败: %v", err)
	} else {
		configFiles = append(configFiles, dirFiles...)
	}

	// 然后处理每个子目录
	for _, dirEntry := range testDirs {
		if !dirEntry.IsDir() {
			continue
		}

		subDirPath := filepath.Join(testDir, dirEntry.Name())
		if f.cfg.DebugLevel == "true" {
			log.Printf("检查测试子目录: %s\n", subDirPath)
		}

		subDirFiles, err := f.getLocalConfigFilesFromDir(subDirPath, supportedTypes)
		if err != nil {
			log.Printf("从测试子目录 %s 获取配置文件失败: %v", subDirPath, err)
			continue
		}

		configFiles = append(configFiles, subDirFiles...)
	}

	if f.cfg.DebugLevel == "true" {
		log.Printf("共获取到 %d 个测试配置文件\n", len(configFiles))
	}

	return configFiles, nil
}

// getLocalConfigFilesFromDir 从本地目录获取配置文件
func (f *Fetcher) getLocalConfigFilesFromDir(dirPath string, supportedTypes []string) ([]*model.ConfigFile, error) {
	files, err := os.ReadDir(dirPath)
	if err != nil {
		return nil, fmt.Errorf("读取目录 %s 失败: %w", dirPath, err)
	}

	var configFiles []*model.ConfigFile

	// 筛选配置文件
	for _, file := range files {
		if file.IsDir() {
			continue
		}

		filename := file.Name()
		fileType := getConfigFileType(filename)

		// 仅处理支持的配置文件类型
		if fileType == "" || !isTypeSupported(fileType, supportedTypes) {
			continue
		}

		if f.cfg.DebugLevel == "true" {
			log.Printf("发现本地配置文件: %s, 类型: %s\n", filename, fileType)
		}

		// 读取文件内容
		filePath := filepath.Join(dirPath, filename)
		// 注意：本地文件读取不需要替换路径分隔符，因为这是操作系统文件系统操作

		content, err := os.ReadFile(filePath)
		if err != nil {
			log.Printf("读取文件 %s 失败: %v", filePath, err)
			continue
		}

		// 检查文件内容是否为空
		if len(content) == 0 {
			log.Printf("文件 %s 内容为空", filePath)
			continue
		}

		// 记录文件基本信息（调试用）
		if f.cfg.DebugLevel == "true" {
			previewLen := 20
			if len(content) < previewLen {
				previewLen = len(content)
			}
			log.Printf("文件 %s 大小: %d 字节, 前%d个字节: %v",
				filename, len(content), previewLen, content[:previewLen])
		}

		// 创建配置文件记录
		now := time.Now()
		configFile := &model.ConfigFile{
			Filename:    filename,
			FileType:    fileType,
			Content:     content,
			Status:      "pending",
			FetchTime:   now,
			RemotePath:  filepath.Join(dirPath, filename), // 本地测试文件路径作为远程路径
			RequestTime: now,                              // 设置请求时间
		}

		configFiles = append(configFiles, configFile)

		if f.cfg.DebugLevel == "true" {
			log.Printf("成功获取本地配置文件: %s, 大小: %d 字节, 路径: %s\n",
				filename, len(content), configFile.RemotePath)
		}
	}

	return configFiles, nil
}

// getSubDirectories 获取指定目录下的所有子目录
func (f *Fetcher) getSubDirectories(remotePath string) ([]string, error) {
	// 修正：将Windows路径分隔符(\)替换为URL路径分隔符(/)
	normalizedRemotePath := strings.ReplaceAll(remotePath, "\\", "/")

	// 从远程获取目录列表
	files, err := f.transporter.List(normalizedRemotePath)
	if err != nil {
		return nil, fmt.Errorf("获取目录列表失败: %w", err)
	}

	var dirs []string
	for _, file := range files {
		// 只保留目录
		if file.IsDir {
			dirs = append(dirs, file.Name)
			if f.cfg.DebugLevel == "true" {
				log.Printf("发现子目录: %s\n", file.Name)
			}
		}
	}

	return dirs, nil
}

// getConfigFilesFromDir 从指定目录获取配置文件（增量下载版本）
func (f *Fetcher) getConfigFilesFromDir(remoteDirPath string, supportedTypes []string) ([]*model.ConfigFile, error) {
	log.Printf("[INFO] 开始获取远程配置文件，使用增量下载机制")

	// 读取本地时间戳记录
	lastTimestamp, err := f.ReadLastTimestamp()
	if err != nil {
		log.Printf("[ERROR] 读取本地时间戳失败: %v", err)
		return nil, fmt.Errorf("读取本地时间戳失败: %w", err)
	}
	log.Printf("[INFO] 本地时间戳记录: %s", lastTimestamp)

	// 从远程获取文件列表
	files, err := f.transporter.List(remoteDirPath)
	if err != nil {
		return nil, fmt.Errorf("获取文件列表失败: %w", err)
	}
	log.Printf("[INFO] 远程文件总数: %d", len(files))

	var configFiles []*model.ConfigFile
	var listErrors []error
	var newTimestamps []string // 收集本次下载的所有文件时间戳

	// 筛选配置文件
	for _, file := range files {
		// 跳过目录
		if file.IsDir {
			continue
		}

		filename := file.Name
		fileType := getConfigFileType(filename)

		// 仅处理支持的配置文件类型
		if fileType == "" || !isTypeSupported(fileType, supportedTypes) {
			if f.cfg.DebugLevel == "true" {
				log.Printf("[DEBUG] 跳过不支持的文件类型: %s", filename)
			}
			continue
		}

		// 从文件名提取时间戳
		fileTimestamp, err := extractTimestampFromFilename(filename)
		if err != nil {
			log.Printf("[WARN] 无法从文件名提取时间戳，跳过文件: %s, 错误: %v", filename, err)
			continue
		}

		// 检查时间戳是否晚于本地记录
		if compareTimestamp(fileTimestamp, lastTimestamp) <= 0 {
			if f.cfg.DebugLevel == "true" {
				log.Printf("[DEBUG] 文件时间戳不晚于本地记录，跳过: %s (文件时间戳: %s, 本地时间戳: %s)",
					filename, fileTimestamp, lastTimestamp)
			}
			continue
		}

		log.Printf("[INFO] 发现增量配置文件: %s, 类型: %s, 时间戳: %s", filename, fileType, fileTimestamp)
		newTimestamps = append(newTimestamps, fileTimestamp)

		// 远程文件完整路径
		remoteFilePath := filepath.Join(remoteDirPath, filename)
		// 修正：将Windows路径分隔符(\)替换为URL路径分隔符(/)
		remoteFilePath = strings.ReplaceAll(remoteFilePath, "\\", "/")

		// 下载文件
		localPath := filepath.Join(f.downloadDir, filename)
		log.Printf("[INFO] 开始下载文件: %s", remoteFilePath)
		if err := f.transporter.Download(remoteFilePath, localPath); err != nil {
			log.Printf("[ERROR] 下载文件 %s 失败: %v", remoteFilePath, err)
			listErrors = append(listErrors, fmt.Errorf("下载文件 %s 失败: %v", remoteFilePath, err))
			continue
		}
		log.Printf("[INFO] 文件下载成功: %s", filename)

		// 读取文件内容
		content, err := os.ReadFile(localPath)
		if err != nil {
			log.Printf("[ERROR] 读取文件 %s 失败: %v", localPath, err)
			listErrors = append(listErrors, fmt.Errorf("读取文件 %s 失败: %v", localPath, err))
			continue
		}

		// 检查文件内容是否为空
		if len(content) == 0 {
			log.Printf("[ERROR] 文件 %s 内容为空", localPath)
			listErrors = append(listErrors, fmt.Errorf("文件 %s 内容为空", localPath))
			continue
		}

		// 记录文件基本信息（调试用）
		if f.cfg.DebugLevel == "true" {
			previewLen := 20
			if len(content) < previewLen {
				previewLen = len(content)
			}
			log.Printf("文件 %s 大小: %d 字节, 前%d个字节: %v",
				filename, len(content), previewLen, content[:previewLen])
		}

		// 创建配置文件记录
		now := time.Now()
		configFile := &model.ConfigFile{
			Filename:    filename,
			FileType:    fileType,
			Content:     content,
			Status:      "pending",
			FetchTime:   now,
			RemotePath:  remoteFilePath, // 保存远程路径，用于后续删除
			RequestTime: now,            // 设置请求时间
		}

		configFiles = append(configFiles, configFile)

		if f.cfg.DebugLevel == "true" {
			log.Printf("成功获取配置文件: %s, 大小: %d 字节, 远程路径: %s\n",
				filename, len(content), remoteFilePath)
		}
	}

	// 更新本地时间戳记录
	if len(newTimestamps) > 0 {
		// 找出最新的时间戳
		latestTimestamp := newTimestamps[0]
		for _, ts := range newTimestamps {
			if compareTimestamp(ts, latestTimestamp) > 0 {
				latestTimestamp = ts
			}
		}

		// 更新本地时间戳记录
		if err := f.UpdateLastTimestamp(latestTimestamp); err != nil {
			log.Printf("[ERROR] 更新本地时间戳失败: %v", err)
			// 不因时间戳更新失败而中断整个流程，仅记录错误
		} else {
			log.Printf("[INFO] 成功更新本地时间戳: %s", latestTimestamp)
		}
	}

	// 如果有错误，但至少获取了一些配置文件，记录错误但返回部分结果
	if len(listErrors) > 0 {
		errMsg := fmt.Sprintf("获取配置文件过程中出现 %d 个错误", len(listErrors))
		log.Printf("[WARN] %s - 第一个错误: %v", errMsg, listErrors[0])

		// 如果完全没有成功获取任何文件，则返回错误
		if len(configFiles) == 0 {
			return nil, fmt.Errorf("%s", errMsg)
		}

		// 否则，记录警告但返回部分结果
		log.Printf("[WARN] %s，但成功获取了 %d 个文件", errMsg, len(configFiles))
	}

	log.Printf("[INFO] 增量下载完成，本次获取 %d 个配置文件", len(configFiles))
	return configFiles, nil
}

// 检查文件类型是否在支持列表中
func isTypeSupported(fileType string, supportedTypes []string) bool {
	for _, t := range supportedTypes {
		if t == fileType {
			return true
		}
	}
	return false
}

// 根据文件名获取配置文件类型
func getConfigFileType(filename string) string {
	lowerName := strings.ToLower(filename)

	// 新格式文件名检测（根据文件名前缀确定类型）
	if strings.HasPrefix(lowerName, "终端新增") {
		return "terminal"
	} else if strings.HasPrefix(lowerName, "终端删除") {
		return "del_terminal"
	} else if strings.HasPrefix(lowerName, "终端通信") {
		return "terminal_connect"
	} else if strings.HasPrefix(lowerName, "网关新增") {
		return "gateway"
	} else if strings.HasPrefix(lowerName, "网关删除") {
		return "del_gateway"
	}

	return ""
}

// Close 关闭获取器
func (f *Fetcher) Close() error {
	if f.transporter != nil {
		return f.transporter.Close()
	}
	return nil
}

// 目录和时间的关联结构
type dirWithTime struct {
	name string
	time time.Time
}

// parseDirNameToTime 将目录名解析为时间
// 支持格式: "20060102150405" 或 "config_20060102150405" 等包含14位数字时间戳的格式
func parseDirNameToTime(dirName string) (time.Time, error) {
	// 提取目录名中的数字部分
	re := regexp.MustCompile(`\d{14}`)
	matches := re.FindString(dirName)
	if matches == "" {
		return time.Time{}, fmt.Errorf("目录名 %s 不包含有效的时间戳格式", dirName)
	}

	// 解析时间戳
	return time.ParseInLocation("20060102150405", matches, time.Local)
}

// getLastProcessTimestamp 获取上次处理的时间戳
func (f *Fetcher) getLastProcessTimestamp() (time.Time, error) {
	db, err := database.GetDB()
	if err != nil {
		return time.Time{}, fmt.Errorf("获取数据库连接失败: %w", err)
	}

	var timestamp model.LastProcessTimestamp
	// 修复：使用反引号包围'key'字段，防止MySQL关键字冲突
	result := db.Where("`Key` = ?", "config_fetch").First(&timestamp)
	if result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			// 如果记录不存在，返回零值时间
			return time.Time{}, nil
		}
		return time.Time{}, fmt.Errorf("查询时间戳记录失败: %w", result.Error)
	}

	return timestamp.Timestamp, nil
}

// updateLastProcessTimestamp 更新处理时间戳
func (f *Fetcher) updateLastProcessTimestamp(timestamp time.Time) error {
	db, err := database.GetDB()
	if err != nil {
		return fmt.Errorf("获取数据库连接失败: %w", err)
	}

	// 使用upsert操作更新或创建记录
	// 修复：使用反引号包围'key'字段，防止MySQL关键字冲突
	result := db.Model(&model.LastProcessTimestamp{}).
		Where("`Key` = ?", "config_fetch").
		Assign(model.LastProcessTimestamp{
			Key:       "config_fetch",
			Timestamp: timestamp,
			UpdatedAt: time.Now(),
		}).
		FirstOrCreate(&model.LastProcessTimestamp{})

	if result.Error != nil {
		return fmt.Errorf("更新时间戳记录失败: %w", result.Error)
	}

	return nil
}

// parseFilenameToTime 从文件名解析时间戳
// 文件名格式预期为: 类型名称YYYYMMDDHHMISS.json，例如: 终端删除20250415232033.json
func parseFilenameToTime(filename string) (time.Time, error) {
	// 获取文件名（不含路径和扩展名）
	baseName := filepath.Base(filename)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)

	// 尝试提取时间戳部分（假设时间戳是文件名末尾的14位数字）
	re := regexp.MustCompile(`(\d{14})$`)
	matches := re.FindStringSubmatch(nameWithoutExt)
	if len(matches) < 2 {
		return time.Time{}, fmt.Errorf("文件名 %s 中未找到有效的时间戳格式", filename)
	}

	// 解析时间戳
	timestampStr := matches[1]
	return time.ParseInLocation("20060102150405", timestampStr, time.Local)
}

// DeleteRemoteFiles 删除已处理的远程文件
// 注意：此功能已在增量下载机制重构中移除，现在仅返回错误信息
func (f *Fetcher) DeleteRemoteFiles(configFiles []*model.ConfigFile) error {
	log.Printf("[INFO] 远程文件删除功能已移除，改为增量下载机制，不再删除远程文件")
	log.Printf("[INFO] 本次跳过删除 %d 个远程文件", len(configFiles))
	return fmt.Errorf("远程文件删除功能已移除，系统现在使用增量下载机制，远程文件将被保留")
}

// ==================== 时间戳管理方法 ====================

// ReadLastTimestamp 读取本地时间戳记录
func (f *Fetcher) ReadLastTimestamp() (string, error) {
	timestampFile := f.cfg.ConfigManager.ConfigFileManager.TimestampFile
	if timestampFile == "" {
		timestampFile = "configmanager/config_exec/last_timestamp.txt"
	}

	// 检查文件是否存在
	if _, err := os.Stat(timestampFile); os.IsNotExist(err) {
		// 文件不存在，返回初始时间戳
		initialTimestamp := f.cfg.ConfigManager.ConfigFileManager.InitialTimestamp
		if initialTimestamp == "" {
			initialTimestamp = "20000000000000"
		}
		log.Printf("[INFO] 时间戳文件不存在，使用初始时间戳: %s", initialTimestamp)
		return initialTimestamp, nil
	}

	// 读取文件内容
	content, err := os.ReadFile(timestampFile)
	if err != nil {
		log.Printf("[ERROR] 读取时间戳文件失败: %v", err)
		return "", fmt.Errorf("读取时间戳文件失败: %w", err)
	}

	timestamp := strings.TrimSpace(string(content))
	if timestamp == "" {
		// 文件为空，返回初始时间戳
		initialTimestamp := f.cfg.ConfigManager.ConfigFileManager.InitialTimestamp
		if initialTimestamp == "" {
			initialTimestamp = "20000000000000"
		}
		log.Printf("[INFO] 时间戳文件为空，使用初始时间戳: %s", initialTimestamp)
		return initialTimestamp, nil
	}

	log.Printf("[INFO] 读取到本地时间戳: %s", timestamp)
	return timestamp, nil
}

// UpdateLastTimestamp 更新本地时间戳记录
func (f *Fetcher) UpdateLastTimestamp(timestamp string) error {
	timestampFile := f.cfg.ConfigManager.ConfigFileManager.TimestampFile
	if timestampFile == "" {
		timestampFile = "configmanager/config_exec/last_timestamp.txt"
	}

	// 确保目录存在
	dir := filepath.Dir(timestampFile)
	if err := os.MkdirAll(dir, 0755); err != nil {
		log.Printf("[ERROR] 创建时间戳文件目录失败: %v", err)
		return fmt.Errorf("创建时间戳文件目录失败: %w", err)
	}

	// 写入时间戳
	if err := os.WriteFile(timestampFile, []byte(timestamp), 0644); err != nil {
		log.Printf("[ERROR] 写入时间戳文件失败: %v", err)
		return fmt.Errorf("写入时间戳文件失败: %w", err)
	}

	log.Printf("[INFO] 成功更新本地时间戳: %s", timestamp)
	return nil
}

// compareTimestamp 比较两个时间戳的大小
// 返回值：-1表示timestamp1 < timestamp2，0表示相等，1表示timestamp1 > timestamp2
func compareTimestamp(timestamp1, timestamp2 string) int {
	if timestamp1 < timestamp2 {
		return -1
	} else if timestamp1 > timestamp2 {
		return 1
	}
	return 0
}

// extractTimestampFromFilename 从文件名中提取时间戳字符串
// 文件名格式：操作类型+时间戳.json，例如：终端新增20250405111412.json
func extractTimestampFromFilename(filename string) (string, error) {
	// 获取文件名（不含路径和扩展名）
	baseName := filepath.Base(filename)
	ext := filepath.Ext(baseName)
	nameWithoutExt := strings.TrimSuffix(baseName, ext)

	// 尝试提取时间戳部分（假设时间戳是文件名末尾的14位数字）
	re := regexp.MustCompile(`(\d{14})$`)
	matches := re.FindStringSubmatch(nameWithoutExt)
	if len(matches) < 2 {
		return "", fmt.Errorf("文件名 %s 中未找到有效的时间戳格式", filename)
	}

	return matches[1], nil
}
